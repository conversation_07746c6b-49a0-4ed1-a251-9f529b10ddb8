import json
import csv

def generate_relationship_header(config):
    """Generuje hlavičku pro CSV soubor se vztahy a jejich vlastnostmi."""
    type_mapping = config['neo4j_type_mapping']

    start_id_header = f":START_ID({config['start_node']['id_space']})"
    end_id_header = f":END_ID({config['end_node']['id_space']})"
    type_header = ":TYPE"

    prop_headers = []
    for prop, model_type in config.get('properties', {}).items():
        neo4j_type = type_mapping.get(model_type, model_type.lower())
        prop_headers.append(f"{prop}:{neo4j_type}")

    return [start_id_header, end_id_header] + prop_headers + [type_header]

def run_conversion(config_key):
    """
    Extrahuje vztahy a jejich vlastnosti ze zdrojového JSON souboru
    a uloží je do CSV pro import do Neo4j.
    """
    CONFIG_FILE = 'config.json'

    print(f"Zahajuji extrakci vztahů s vlastnostmi: {config_key}")

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            config = config_data[config_key]
        print("Konfigurační soubor úspěšně načten.")
    except (FileNotFoundError, KeyError) as e:
        print(f"CHYBA: Nepodařilo se načíst konfiguraci. {e}")
        return

    source_file = config['source_file']
    output_file = config['output_file']

    start_node_key = config['start_node']['source_key']
    end_node_key = config['end_node']['source_key']
    rel_type = config['relationship_type']

    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        # Zpracováváme vnořenou strukturu
        rocniky = data.get('položky', [{}])[0].get('právní-akt-ročník', [])
        print(f"Zdrojový soubor '{source_file}' načten, nalezeno {len(rocniky)} ročníků.")
    except (FileNotFoundError, IndexError):
        print(f"CHYBA: Zdrojový soubor '{source_file}' nebyl nalezen nebo má nečekanou strukturu.")
        return
    except json.JSONDecodeError:
        print(f"CHYBA: Zdrojový soubor '{source_file}' není validní JSON.")
        return

    header = generate_relationship_header(config)

    records = []
    for rocnik in rocniky:
        start_id = rocnik.get(start_node_key)

        for akt in rocnik.get('právní-akt', []):
            end_id = akt.get(end_node_key)

            if start_id and end_id:
                record = {
                    header[0]: start_id,
                    header[1]: end_id,
                    header[-1]: rel_type
                }
                # Zpracování vlastností vztahu
                prop_index = 2
                for prop_name, model_type in config.get('properties', {}).items():
                    source_key = config['source_json_key_mapping'][prop_name]
                    value = akt.get(source_key, None)
                    record[header[prop_index]] = value
                    prop_index += 1

                records.append(record)

    if not records:
        print("Varování: Nebyly nalezeny žádné vztahy k zápisu.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=header)
            writer.writeheader()
            writer.writerows(records)
        print(f"Úspěšně vygenerován soubor vztahů '{output_file}' s {len(records)} záznamy.")
    except IOError as e:
        print(f"CHYBA: Nepodařilo se zapsat do souboru '{output_file}'. {e}")

if __name__ == '__main__':
    run_conversion('Vztah_Rocnik_Akt')
