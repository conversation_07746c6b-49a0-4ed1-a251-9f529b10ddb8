import json
import csv
from datetime import datetime

def generate_expected_header(config):
    """<PERSON>ruje očekávanou hlav<PERSON> pro CSV soubor s entitami."""
    type_mapping = config['neo4j_type_mapping']
    
    # ID sloupec
    header = [f"{config['unique_id_source_key']}:ID({config['id_space']})"]
    
    # Vlastnosti s datovými typy
    for prop, model_type in config['properties'].items():
        neo4j_type = type_mapping.get(model_type, model_type.lower())
        header.append(f"{prop}:{neo4j_type}")
    
    # Label sloupec
    header.append(":LABEL")
    
    return header

def is_valid_type(value, model_type):
    """Robustní funkce pro validaci datových typů."""
    if not value:  # Prázdné hodnoty jsou povoleny
        return True
    
    if model_type == 'Integer':
        try:
            int(value)
            return True
        except (ValueError, TypeError):
            return False
    elif model_type == 'String':
        return isinstance(value, str)
    elif model_type == 'Date':
        try:
            datetime.strptime(value, '%Y-%m-%d')
            return True
        except (ValueError, TypeError):
            return False
    elif model_type == 'DateTime':
        for fmt in ('%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
            try:
                datetime.strptime(value, fmt)
                return True
            except (ValueError, TypeError):
                continue
        return False
    elif model_type == 'Boolean':
        return value.lower() in ['true', 'false']
    return False

def run_audit(config_key):
    """
    Načte vygenerovaný CSV soubor a zkontroluje jeho strukturu a datové typy
    proti pravidlům definovaným v konfiguračním souboru.
    """
    CONFIG_FILE = 'config.json'

    print("="*50)
    print(f"ZAHÁJENÍ AUDITU pro entitu: {config_key}")
    print("="*50)

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            config = config_data[config_key]
        print("[INFO] Konfigurační soubor úspěšně načten.")
    except (FileNotFoundError, KeyError) as e:
        print(f"[FAIL] Kritická chyba: Nepodařilo se načíst konfiguraci. {e}")
        return

    csv_file = config['output_file']
    errors_found = 0

    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)

            try:
                actual_header = next(reader)
            except StopIteration:
                print(f"[FAIL] Kritická chyba: Soubor '{csv_file}' je prázdný.")
                return

            expected_header = generate_expected_header(config)

            if actual_header != expected_header:
                print("[FAIL] Audit hlavičky selhal!")
                print(f"  Očekávaná hlavička: {expected_header}")
                print(f"  Nalezená hlavička:  {actual_header}")
                return
            else:
                print("[PASS] Audit hlavičky úspěšný.")

            print("[INFO] Zahajuji audit datových typů v řádcích...")
            for row_num, row in enumerate(reader, 2):
                if len(row) != len(expected_header):
                    print(f"[FAIL] Řádek {row_num}: Nesprávný počet sloupců. Očekáváno {len(expected_header)}, nalezeno {len(row)}.")
                    errors_found += 1
                    continue

                for col_num, cell_value in enumerate(row):
                    if not cell_value:
                        continue

                    header_name = expected_header[col_num]

                    # Přeskočíme ID a LABEL sloupce
                    if ':' not in header_name or header_name.startswith(':'):
                        continue

                    prop_name_from_header = header_name.split(':')[0]

                    model_type = config['properties'].get(prop_name_from_header)
                    if not model_type:
                        continue

                    if not is_valid_type(cell_value, model_type):
                        print(f"[FAIL] Řádek {row_num}, Sloupec '{header_name}': Hodnota '{cell_value}' neodpovídá typu '{model_type}'.")
                        errors_found += 1

    except FileNotFoundError:
        print(f"[FAIL] Kritická chyba: Auditovaný soubor '{csv_file}' nebyl nalezen.")
        return

    print("="*50)
    if errors_found == 0:
        print(f"VÝSLEDEK AUDITU: [PASS]")
        print("Soubor je v souladu se schváleným modelem.")
    else:
        print(f"VÝSLEDEK AUDITU: [FAIL]")
        print(f"Nalezeno celkem {errors_found} chyb.")
    print("="*50)

if __name__ == '__main__':
    run_audit('PravniAktZneni')
