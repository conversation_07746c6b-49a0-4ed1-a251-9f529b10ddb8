# Konverze JSON-LD souborů E-SBIRKA

Tento balík scriptů slouží k převodu JSON-LD souborů E-SBIRKA dat do strukturovaného formátu podle analýzy v `E-SBIRKA DATA!!! .md`.

## <PERSON><PERSON><PERSON><PERSON> scriptů

### 🚀 Hlavní orchestrační script
- **`run_all_conversions.py`** - Spustí všechny konverze v správném pořadí

### 📋 Specializované konvertory
- **`convert_pravni_akty.py`** - Konverze právních aktů a jejich znění
- **`convert_ciselniky.py`** - Konverze všech číselníků
- **`convert_czechvoc.py`** - Konverze CzechVOC sémantického slovníku
- **`convert_pomocne_entity.py`** - Konverze pomocných entit (metadata, odkazy, atd.)

### 📊 Pomocné scripty
- **`convert_jsonld_main.py`** - Obecný konvertor (alternativa k orchestračnímu scriptu)

## Struktura zdrojových dat

Scripty očekávají JSON-LD soubory v cestě:
```
F:\nocsv\jsonld\
├── 001PravniAktZneni\          # Adresář s part_*.jsonld soubory
├── 002PravniAkt.jsonld         # Jednotlivý soubor
├── 003PravniAktZneniFragment\  # Adresář s part_*.jsonld soubory
├── 004PravniAktFragment\       # Adresář s part_*.jsonld soubory
├── 005PravniAktKomentarFragmentu.jsonld
├── 006PravniAktMetadata.jsonld
├── 007PravniAktKonsolidacniVazba.jsonld
├── 008PravniAktOdkaz\          # Adresář s part_*.jsonld soubory
├── 009PravniAktSouvisejiciDokument.jsonld
├── 010PravniAktBinarniSoubor.jsonld
├── 011PravniAktRocnikSbirky.jsonld
├── 012CiselnikSbirka.jsonld
├── ...                         # Další číselníky 013-029
├── 030CzechVOCKoncept.jsonld
├── ...                         # Další CzechVOC entity 031-035
├── 036CiselnikCzechVocSchemaKonceptu.jsonld
├── ...                         # Další CzechVOC číselníky 037-042
├── 043PravniAktDigitalniReplika.jsonld
├── 044CiselnikDruhPravnihoAktu.jsonld
└── 045CiselnikSablonaObsahu.jsonld
```

## Spuštění konverze

### Rychlé spuštění (doporučeno)
```bash
python run_all_conversions.py
```

### Spuštění jednotlivých konvertorů
```bash
# Číselníky
python convert_ciselniky.py

# CzechVOC entity
python convert_czechvoc.py

# Pomocné entity
python convert_pomocne_entity.py

# Právní akty (spustit jako poslední)
python convert_pravni_akty.py
```

## Výstupní struktura

Konvertovaná data se ukládají do:
```
./converted_data/
├── pravni_akty/
│   └── pravni_akty_converted.json
├── ciselniky/
│   ├── ciselniky_converted.json
│   ├── cissbirka.json
│   ├── cistypfragmentu.json
│   └── ...                     # Jednotlivé číselníky
├── czechvoc/
│   ├── czechvoc_converted.json
│   ├── cvskoncept.json
│   ├── cvstermin.json
│   └── ...                     # Jednotlivé CzechVOC entity
├── pomocne_entity/
│   ├── pomocne_entity_converted.json
│   ├── pravniaktmetadata.json
│   ├── pravniaktoddkaz.json
│   └── ...                     # Jednotlivé pomocné entity
└── conversion_summary.txt      # Souhrnná zpráva
```

## Mapování entit

### Hlavní entity
- **PravniAkt** - Abstraktní právní akt
- **PravniAktZneni** - Konkrétní znění právního aktu
- **PravniAktFragment** - Abstraktní fragment
- **PravniAktZneniFragment** - Konkrétní fragment znění
- **Sbirka** - Sbírka zákonů
- **PravniAktRocnik** - Ročník sbírky

### Pomocné entity
- **PravniAktMetadata** - Metadata právního aktu
- **PravniAktDigitalniReplika** - Digitální replika (PDF)
- **PravniAktBinarniSoubor** - Binární soubory
- **PravniAktOdkaz** - Odkazy v textu
- **PravniAktKomentarFragmentu** - Komentáře k fragmentům
- **PravniAktSouvisejiciDokument** - Související dokumenty
- **PravniAktVazbaKonsolidacni** - Konsolidační vazby

### Vnořené entity
- **ZneniCastka** - Částka sbírky
- **UcinnostTextem** - Textový popis účinnosti
- **BinarniSouborObsah** - Obsah binárního souboru
- **MetadataPozastaveniProvadeni** - Pozastavení provádění
- **VazbaKonsolidacniKonflikt** - Konflikty v konsolidaci

### CzechVOC entity
- **CvsKoncept** - Sémantický koncept
- **CvsTermin** - Termín
- **CvsDefiniceTerminu** - Definice termínu
- **CvsPoznamkaKonceptu** - Poznámka ke konceptu
- **CvsVazbaTerminHierarchie** - Hierarchické vztahy
- **CvsVazbaTerminSouvisejici** - Asociativní vztahy
- **KonceptOdkaz** - Externí odkazy konceptů

### Číselníkové entity
- **Ciselnik** - Kontejner číselníku
- **Cis*** - Jednotlivé položky číselníků (26 typů)

## Formát výstupních dat

Každá entita má strukturu:
```json
{
  "entity_type": "NazevEntity",
  "iri": "unique_identifier",
  "properties": {
    "vlastnost1": "hodnota1",
    "vlastnost2": "hodnota2"
  },
  "relationships": {
    "TYP_VZTAHU": ["target_iri1", "target_iri2"]
  },
  "nested_entities": {
    "VnorenaEntita": [...]
  }
}
```

## Vlastnosti entit

Vlastnosti jsou mapovány z původních JSON-LD klíčů do camelCase formátu:
- `akt-číslo-předpisu` → `aktCisloPredpisu`
- `znění-datum-účinnosti-od` → `zneniDatumUcinnostiOd`
- `metadata-název-citace` → `metadataNazevCitace`

## Vztahy mezi entitami

Vztahy jsou definovány pomocí typů:
- `MA_ZNENI` - právní akt má znění
- `JE_ZNENIM_AKTU` - znění patří k aktu
- `OBSAHUJE_FRAGMENT` - znění obsahuje fragment
- `ODKAZUJE_NA_FRAGMENT` - odkaz směřuje na fragment
- atd.

## Logování a chyby

Všechny scripty používají Python logging:
- **INFO** - Průběh zpracování
- **WARNING** - Chybějící soubory (neblokující)
- **ERROR** - Kritické chyby

Logy se zobrazují v konzoli a jsou také zahrnuty v souhrnné zprávě.

## Požadavky

- Python 3.7+
- Standardní knihovny: `json`, `pathlib`, `datetime`, `logging`, `subprocess`
- Přístup k cestě `F:\nocsv\jsonld\` se zdrojovými JSON-LD soubory

## Řešení problémů

### Chybí zdrojové soubory
Zkontrolujte cestu `F:\nocsv\jsonld\` a ujistěte se, že obsahuje všechny potřebné soubory podle `vypis.txt`.

### Chyby při parsování JSON
Zkontrolujte encoding souborů - scripty očekávají UTF-8.

### Nedostatek místa na disku
Výstupní soubory mohou být velké, ujistěte se, že máte dostatek místa.

### Chyby při převodu datumů
Některé datumy mohou mít nestandardní formát - script se pokusí o převod, ale při chybě uloží původní hodnotu.

---

*Vytvořeno na základě analýzy v E-SBIRKA DATA!!! .md*
