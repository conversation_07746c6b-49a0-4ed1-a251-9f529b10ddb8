#!/usr/bin/env python3
"""
Script pro převod JSON-LD souborů CzechVOC entit
Zpracovává sémantický slovník konceptů, term<PERSON><PERSON>, definic a jej<PERSON> v<PERSON>
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

JSONLD_BASE_PATH = r"F:\nocsv\jsonld"
OUTPUT_BASE_PATH = "./converted_data/czechvoc"

class CzechVOCConverter:
    """Konvertor pro CzechVOC entity"""
    
    def __init__(self):
        self.output_path = Path(OUTPUT_BASE_PATH)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Mapování souborů na typy entit
        self.file_mapping = {
            "030CzechVOCKoncept.jsonld": "CvsKoncept",
            "031CzechVOCDefiniceTerminu.jsonld": "CvsDefiniceTerminu",
            "032CzechVOCTermin.jsonld": "CvsTermin",
            "033CzechVOCPoznamkaKonceptu.jsonld": "CvsPoznamkaKonceptu",
            "034CzechVOCVazbaHierarchie.jsonld": "CvsVazbaTerminHierarchie",
            "035CzechVOCVazbaSouvisejiciTermin.jsonld": "CvsVazbaTerminSouvisejici"
        }
    
    def convert_cvs_koncept(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsKoncept entitu"""
        converted = {
            'entity_type': 'CvsKoncept',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        # Mapování vlastností
        property_mapping = {
            'koncept-datum-založení': 'konceptDatumZalozeni',
            'koncept-datum-změny': 'konceptDatumZmeny',
            'koncept-id': 'konceptId',
            'koncept-je-právní-oblast': 'konceptJePravniOblast',
            'koncept-klíč': 'konceptKlic',
            'koncept-verze': 'konceptVerze'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'datum' in orig_key and isinstance(value, str):
                    try:
                        converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'cvs-termín' in data:
            converted['relationships']['MA_TERMIN'] = self._extract_iris(data['cvs-termín'])
            
        if 'cvs-definice-termínu' in data:
            converted['relationships']['MA_DEFINICI'] = self._extract_iris(data['cvs-definice-termínu'])
            
        if 'cvs-poznámka-konceptu' in data:
            converted['relationships']['MA_POZNAMKU'] = self._extract_iris(data['cvs-poznámka-konceptu'])
            
        if 'koncept-odkaz' in data:
            converted['relationships']['MA_ODKAZ'] = self._extract_iris(data['koncept-odkaz'])
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # KonceptOdkaz
        if 'koncept-odkaz' in data and isinstance(data['koncept-odkaz'], (dict, list)):
            converted['nested_entities']['KonceptOdkaz'] = []
            for odkaz in self._ensure_list(data['koncept-odkaz']):
                if isinstance(odkaz, dict):
                    converted['nested_entities']['KonceptOdkaz'].append(self._convert_koncept_odkaz(odkaz))
        
        return converted
    
    def convert_cvs_definice_terminu(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsDefiniceTerminu entitu"""
        converted = {
            'entity_type': 'CvsDefiniceTerminu',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'definice-termínu-id': 'definiceTerminuId',
            'definice-termínu-text': 'definiceTerminuText',
            'definice-termínu-citace': 'definiceTerminuCitace',
            'definice-termínu-typ-jazyka-iri': 'definiceTerminuTypJazykaIri',
            'definice-termínu-platnost-vazby-od': 'definiceTerminuPlatnostVazbyOd',
            'definice-termínu-platnost-vazby-do': 'definiceTerminuPlatnostVazbyDo'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'datum' in orig_key or 'platnost' in orig_key:
                    if isinstance(value, str):
                        try:
                            converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                        except:
                            converted['properties'][new_key] = value
                    else:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'definice-termínu-vazba' in data:
            # Toto je přímý vztah na PravniAktZneniFragment
            converted['relationships']['DEFINOVANO_V_FRAGMENTU'] = self._extract_iris(data['definice-termínu-vazba'])
        
        return converted
    
    def convert_cvs_termin(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsTermin entitu"""
        converted = {
            'entity_type': 'CvsTermin',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'termín-id': 'terminId',
            'termín-je-preferovaný': 'terminJePreferovany',
            'termín-název': 'terminNazev',
            'termín-typ-jazyka-iri': 'terminTypJazykaIri'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        return converted
    
    def convert_cvs_poznamka_konceptu(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsPoznamkaKonceptu entitu"""
        converted = {
            'entity_type': 'CvsPoznamkaKonceptu',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'poznámka-konceptu-id': 'poznamkaKonceptuId',
            'poznámka-konceptu-text': 'poznamkaKonceptuText',
            'poznámka-konceptu-typ-jazyka-iri': 'poznamkaKonceptuTypJazykaIri'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        return converted
    
    def convert_cvs_vazba_termin_hierarchie(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsVazbaTerminHierarchie entitu"""
        converted = {
            'entity_type': 'CvsVazbaTerminHierarchie',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        if 'vazba-termín-hierarchie-id' in data:
            converted['properties']['vazbaTerminHierarchieId'] = data['vazba-termín-hierarchie-id']
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'cvs-koncept-nadřazený' in data:
            converted['relationships']['NADRAZENY_KONCEPT'] = self._extract_iris(data['cvs-koncept-nadřazený'])
            
        if 'cvs-koncept-podřazený' in data:
            converted['relationships']['PODRAZENY_KONCEPT'] = self._extract_iris(data['cvs-koncept-podřazený'])
        
        return converted
    
    def convert_cvs_vazba_termin_souvisejici(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsVazbaTerminSouvisejici entitu"""
        converted = {
            'entity_type': 'CvsVazbaTerminSouvisejici',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'vazba-termín-související-id': 'vazbaTerminSouvisejiciId',
            'vazba-termín-související-poznámka': 'vazbaTerminSouvisejiciPoznamka'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'cvs-koncept-zdroj' in data:
            converted['relationships']['ZDROJOVY_KONCEPT'] = self._extract_iris(data['cvs-koncept-zdroj'])
            
        if 'cvs-koncept-cíl' in data:
            converted['relationships']['CILOVY_KONCEPT'] = self._extract_iris(data['cvs-koncept-cíl'])
        
        return converted
    
    def _convert_koncept_odkaz(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede KonceptOdkaz vnořenou entitu"""
        return {
            'entity_type': 'KonceptOdkaz',
            'iri': data.get('iri'),
            'properties': {
                'konceptOdkazNazev': data.get('koncept-odkaz-název'),
                'konceptOdkazPopis': data.get('koncept-odkaz-popis'),
                'konceptOdkazUrl': data.get('koncept-odkaz-url')
            }
        }
    
    def _extract_iris(self, data: Any) -> List[str]:
        """Extrahuje IRI z dat"""
        if isinstance(data, str):
            return [data]
        elif isinstance(data, dict) and 'iri' in data:
            return [data['iri']]
        elif isinstance(data, list):
            iris = []
            for item in data:
                if isinstance(item, str):
                    iris.append(item)
                elif isinstance(item, dict) and 'iri' in item:
                    iris.append(item['iri'])
            return iris
        return []
    
    def _ensure_list(self, data: Any) -> List[Any]:
        """Zajistí že data jsou v podobě listu"""
        if isinstance(data, list):
            return data
        return [data]
    
    def process_file(self, file_path: str, entity_type: str) -> List[Dict[str, Any]]:
        """Zpracuje soubor CzechVOC entity"""
        logger.info(f"Zpracovávám {file_path} -> {entity_type}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání {file_path}: {e}")
            return []
        
        converted_items = []
        
        # Vyberu správnou konverzi podle typu entity
        converter_map = {
            'CvsKoncept': self.convert_cvs_koncept,
            'CvsDefiniceTerminu': self.convert_cvs_definice_terminu,
            'CvsTermin': self.convert_cvs_termin,
            'CvsPoznamkaKonceptu': self.convert_cvs_poznamka_konceptu,
            'CvsVazbaTerminHierarchie': self.convert_cvs_vazba_termin_hierarchie,
            'CvsVazbaTerminSouvisejici': self.convert_cvs_vazba_termin_souvisejici
        }
        
        converter_func = converter_map.get(entity_type)
        if not converter_func:
            logger.error(f"Neznámý typ entity: {entity_type}")
            return []
        
        # Zpracuj data
        if isinstance(data, list):
            for item in data:
                converted = converter_func(item)
                converted_items.append(converted)
        elif isinstance(data, dict):
            if '@graph' in data:
                graph_data = data['@graph']
                if isinstance(graph_data, list):
                    for item in graph_data:
                        converted = converter_func(item)
                        converted_items.append(converted)
                else:
                    converted = converter_func(graph_data)
                    converted_items.append(converted)
            else:
                converted = converter_func(data)
                converted_items.append(converted)
        
        return converted_items
    
    def process_all_czechvoc(self):
        """Zpracuje všechny CzechVOC entity"""
        all_converted = []
        
        for file_name, entity_type in self.file_mapping.items():
            file_path = os.path.join(JSONLD_BASE_PATH, file_name)
            
            if os.path.isfile(file_path):
                converted_items = self.process_file(file_path, entity_type)
                all_converted.extend(converted_items)
            else:
                logger.warning(f"Soubor {file_path} neexistuje")
        
        # Ulož výsledky
        output_file = self.output_path / "czechvoc_converted.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_converted, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Převedeno {len(all_converted)} CzechVOC entit do {output_file}")
        except Exception as e:
            logger.error(f"Chyba při ukládání: {e}")
        
        # Vytvoř také rozdělené soubory podle typů
        self._save_by_types(all_converted)
    
    def _save_by_types(self, all_converted: List[Dict[str, Any]]):
        """Uloží entity rozdělené podle typů"""
        by_type = {}
        
        for item in all_converted:
            entity_type = item['entity_type']
            if entity_type not in by_type:
                by_type[entity_type] = []
            by_type[entity_type].append(item)
        
        # Ulož každý typ zvlášť
        for entity_type, items in by_type.items():
            output_file = self.output_path / f"{entity_type.lower()}.json"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(items, f, ensure_ascii=False, indent=2, default=str)
                logger.info(f"Uloženo {len(items)} entit typu {entity_type} do {output_file}")
            except Exception as e:
                logger.error(f"Chyba při ukládání {entity_type}: {e}")

def main():
    converter = CzechVOCConverter()
    converter.process_all_czechvoc()

if __name__ == "__main__":
    main()
