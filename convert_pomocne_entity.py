#!/usr/bin/env python3
"""
Script pro převod JSON-LD souborů pomocných entit
Zpracovává metadata, digit<PERSON><PERSON><PERSON> repliky, bin<PERSON><PERSON><PERSON> soubory, kome<PERSON><PERSON><PERSON><PERSON>, od<PERSON><PERSON>, souvise<PERSON><PERSON><PERSON><PERSON> do<PERSON>, atd.
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

JSONLD_BASE_PATH = r"F:\nocsv\jsonld"
OUTPUT_BASE_PATH = "./converted_data/pomocne_entity"

class PomocneEntityConverter:
    """Konvertor pro pomocné entity"""
    
    def __init__(self):
        self.output_path = Path(OUTPUT_BASE_PATH)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Mapování souborů na typy entit
        self.file_mapping = {
            "005PravniAktKomentarFragmentu.jsonld": "PravniAktKomentarFragmentu",
            "006PravniAktMetadata.jsonld": "PravniAktMetadata",
            "007PravniAktKonsolidacniVazba.jsonld": "PravniAktVazbaKonsolidacni",
            "008PravniAktOdkaz": "PravniAktOdkaz",  # Adresář s více soubory
            "009PravniAktSouvisejiciDokument.jsonld": "PravniAktSouvisejiciDokument",
            "010PravniAktBinarniSoubor.jsonld": "PravniAktBinarniSoubor",
            "011PravniAktRocnikSbirky.jsonld": "PravniAktRocnik",
            "043PravniAktDigitalniReplika.jsonld": "PravniAktDigitalniReplika"
        }
    
    def convert_komentar_fragmentu(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktKomentarFragmentu entitu"""
        converted = {
            'entity_type': 'PravniAktKomentarFragmentu',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'komentář-fragmentu-id': 'komentarFragmentuId',
            'komentář-fragmentu-anotace': 'komentarFragmentuAnotace',
            'komentář-fragmentu-název': 'komentarFragmentuNazev',
            'komentář-fragmentu-platnost-od': 'komentarFragmentuPlatnostOd',
            'komentář-fragmentu-platnost-do': 'komentarFragmentuPlatnostDo',
            'komentář-fragmentu-popis': 'komentarFragmentuPopis',
            'komentář-fragmentu-url': 'komentarFragmentuUrl',
            'komentář-fragmentu-zrušen': 'komentarFragmentuZrusen'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'platnost' in orig_key and isinstance(value, str):
                    try:
                        converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        return converted
    
    def convert_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktMetadata entitu"""
        converted = {
            'entity_type': 'PravniAktMetadata',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'metadata-id': 'metadataId',
            'metadata-cislo-predpisu': 'metadataCisloPredpisu',
            'metadata-rok-předpisu': 'metadataRokPredpisu',
            'metadata-citace': 'metadataCitace',
            'metadata-název': 'metadataNazev',
            'metadata-název-citace': 'metadataNazevCitace',
            'metadata-název-zkrácený': 'metadataNazevZkraceny',
            'metadata-datum-schválení': 'metadataDatumSchvaleni',
            'metadata-datum-účinnosti-od': 'metadataDatumUcinnostiOd',
            'metadata-datum-účinnosti-do': 'metadataDatumUcinnostiDo',
            'metadata-datum-platnosti-do': 'metadataDatumPlatnostiDo',
            'metadata-datum-zrušení': 'metadataDatumZruseni',
            'metadata-eli': 'metadataEli',
            'metadata-kód': 'metadataKod'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'datum' in orig_key and isinstance(value, str):
                    try:
                        converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj pole dalších názvů
        if 'metadata-další-název' in data:
            dalsi_nazvy = data['metadata-další-název']
            if isinstance(dalsi_nazvy, list):
                converted['properties']['metadataDalsiNazvy'] = dalsi_nazvy
            else:
                converted['properties']['metadataDalsiNazvy'] = [dalsi_nazvy]
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # MetadataPozastaveniProvadeni
        if 'metadata-pozastavení-provádění' in data:
            converted['nested_entities']['MetadataPozastaveniProvadeni'] = []
            for pozastaveni in self._ensure_list(data['metadata-pozastavení-provádění']):
                converted['nested_entities']['MetadataPozastaveniProvadeni'].append(
                    self._convert_pozastaveni_provadeni(pozastaveni)
                )
        
        # UcinnostTextem pro účinnost a zrušení
        if 'metadata-účinnost-textem' in data:
            converted['nested_entities']['UcinnostTextem'] = self._convert_ucinnost_textem(data['metadata-účinnost-textem'])
            
        if 'metadata-zrušení-textem' in data:
            if 'UcinnostTextem' not in converted['nested_entities']:
                converted['nested_entities']['UcinnostTextem'] = []
            elif not isinstance(converted['nested_entities']['UcinnostTextem'], list):
                converted['nested_entities']['UcinnostTextem'] = [converted['nested_entities']['UcinnostTextem']]
            converted['nested_entities']['UcinnostTextem'].append(
                self._convert_ucinnost_textem(data['metadata-zrušení-textem'])
            )
        
        return converted
    
    def convert_vazba_konsolidacni(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktVazbaKonsolidacni entitu"""
        converted = {
            'entity_type': 'PravniAktVazbaKonsolidacni',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'vazba-konsolidační-id': 'vazbaKonsolidacniId',
            'vazba-konsolidační-účinnost-od': 'vazbaKonsolidacniUcinnostOd'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'účinnost' in orig_key and isinstance(value, str):
                    try:
                        converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'právní-akt-znění-fragment-zdroj' in data:
            converted['relationships']['ZDROJOVY_FRAGMENT'] = self._extract_iris(data['právní-akt-znění-fragment-zdroj'])
            
        if 'právní-akt-znění-fragment-cíl' in data:
            converted['relationships']['CILOVY_FRAGMENT'] = self._extract_iris(data['právní-akt-znění-fragment-cíl'])
            
        if 'právní-akt-znění-fragment-novelizační' in data:
            converted['relationships']['NOVELIZACNI_FRAGMENT'] = self._extract_iris(data['právní-akt-znění-fragment-novelizační'])
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # VazbaKonsolidacniKonflikt
        if 'vazba-konsolidační-konflikt' in data:
            converted['nested_entities']['VazbaKonsolidacniKonflikt'] = []
            for konflikt in self._ensure_list(data['vazba-konsolidační-konflikt']):
                converted['nested_entities']['VazbaKonsolidacniKonflikt'].append(
                    self._convert_konsolidacni_konflikt(konflikt)
                )
        
        # UcinnostTextem
        if 'vazba-konsolidační-účinnost-textem' in data:
            converted['nested_entities']['UcinnostTextem'] = self._convert_ucinnost_textem(data['vazba-konsolidační-účinnost-textem'])
        
        return converted
    
    def convert_souvisejici_dokument(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktSouvisejiciDokument entitu"""
        converted = {
            'entity_type': 'PravniAktSouvisejiciDokument',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'související-dokument-id': 'souvisejiciDokumentId',
            'související-dokument-název': 'souvisejiciDokumentNazev',
            'související-dokument-dokument-id': 'souvisejiciDokumentDokumentId'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        return converted
    
    def convert_binarni_soubor(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktBinarniSoubor entitu"""
        converted = {
            'entity_type': 'PravniAktBinarniSoubor',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'binární-soubor-id': 'binarniSouborId',
            'binární-soubor-název': 'binarniSouborNazev',
            'binární-soubor-vytvoření': 'binarniSouborVytvoreni'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'vytvoření' in orig_key and isinstance(value, str):
                    try:
                        converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # BinarniSouborObsah
        if 'binární-soubor-obsah' in data:
            converted['nested_entities']['BinarniSouborObsah'] = []
            for obsah in self._ensure_list(data['binární-soubor-obsah']):
                converted['nested_entities']['BinarniSouborObsah'].append(
                    self._convert_binarni_soubor_obsah(obsah)
                )
        
        return converted
    
    def convert_odkaz(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktOdkaz entitu"""
        converted = {
            'entity_type': 'PravniAktOdkaz',
            'iri': data.get('iri'),
            'properties': {}
        }

        property_mapping = {
            'odkaz-id': 'odkazId',
            'odkaz-base-id': 'odkazBaseId',
            'odkaz-base-iri': 'odkazBaseIri',
            'odkaz-citace-cíl': 'odkazCitaceCil',
            'odkaz-je-externí': 'odkazJeExterni',
            'odkaz-je-statický': 'odkazJeStaticky',
            'odkaz-vyžaduje-revizi': 'odkazVyžadujeRevizi',
            'odkaz-adresa-url': 'odkazAdresaUrl'
        }

        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]

        # Zpracuj vztahy
        converted['relationships'] = {}

        if 'právní-akt-znění-fragment-cíl' in data:
            # Vztah s vlastností kotva
            target_fragment = data['právní-akt-znění-fragment-cíl']
            if isinstance(target_fragment, dict):
                converted['relationships']['ODKAZUJE_NA_FRAGMENT'] = [{
                    'target_iri': target_fragment.get('iri'),
                    'kotva': target_fragment.get('kotva')
                }]
            else:
                converted['relationships']['ODKAZUJE_NA_FRAGMENT'] = [{'target_iri': target_fragment}]

        return converted

    def convert_rocnik_sbirky(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktRocnik a Sbirka entity"""
        # Zpracuj podle typu
        if data.get('@type') == 'sbírka' or 'sbírka' in str(data.get('@type', '')).lower():
            converted = {
                'entity_type': 'Sbirka',
                'iri': data.get('iri'),
                'properties': {
                    'sbirkaKod': data.get('sbírka-kód'),
                    'sbirkaId': data.get('sbírka-id')
                }
            }
        else:
            # PravniAktRocnik
            converted = {
                'entity_type': 'PravniAktRocnik',
                'iri': data.get('iri'),
                'properties': {
                    'sbirkaKod': data.get('sbírka-kód'),
                    'rocnik': data.get('ročník'),
                    'nazevRocniku': data.get('název-ročníku')
                }
            }

        return converted
    
    def convert_digitalni_replika(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktDigitalniReplika entitu"""
        converted = {
            'entity_type': 'PravniAktDigitalniReplika',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'replika-id': 'replikaId',
            'replika-název': 'replikaNazev',
            'replika-velikost': 'replikaVelikost',
            'replika-počet-stran': 'replikaPocetStran',
            'replika-žádost-url': 'replikaZadostUrl',
            'replika-obsah-url': 'replikaObsahUrl'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        return converted
    
    def _convert_pozastaveni_provadeni(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede MetadataPozastaveniProvadeni vnořenou entitu"""
        return {
            'entity_type': 'MetadataPozastaveniProvadeni',
            'properties': {
                'datumOd': data.get('datum-od'),
                'datumDo': data.get('datum-do'),
                'jePozastaveni': data.get('je-pozastavení')
            }
        }
    
    def _convert_ucinnost_textem(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede UcinnostTextem vnořenou entitu"""
        return {
            'entity_type': 'UcinnostTextem',
            'properties': {
                'ucinnostTextem': data.get('účinnost-textem'),
                'ucinnostTextemNazev': data.get('účinnost-textem-název'),
                'ucinnostTextemPracovniDatum': data.get('účinnost-textem-pracovní-datum'),
                'ucinnostTextemSkutecneDatum': data.get('účinnost-textem-skutečné-datum')
            }
        }
    
    def _convert_konsolidacni_konflikt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede VazbaKonsolidacniKonflikt vnořenou entitu"""
        return {
            'entity_type': 'VazbaKonsolidacniKonflikt',
            'properties': {
                'konfliktAutor': data.get('konflikt-autor'),
                'konfliktPopis': data.get('konflikt-popis'),
                'konfliktVytvoreni': data.get('konflikt-vytvoření')
            }
        }
    
    def _convert_binarni_soubor_obsah(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede BinarniSouborObsah vnořenou entitu"""
        return {
            'entity_type': 'BinarniSouborObsah',
            'properties': {
                'obsahUrl': data.get('obsah-url'),
                'obsahTyp': data.get('obsah-typ'),
                'obsahVelikost': data.get('obsah-velikost'),
                'obsahPocetStran': data.get('obsah-počet-stran')
            }
        }
    
    def _extract_iris(self, data: Any) -> List[str]:
        """Extrahuje IRI z dat"""
        if isinstance(data, str):
            return [data]
        elif isinstance(data, dict) and 'iri' in data:
            return [data['iri']]
        elif isinstance(data, list):
            iris = []
            for item in data:
                if isinstance(item, str):
                    iris.append(item)
                elif isinstance(item, dict) and 'iri' in item:
                    iris.append(item['iri'])
            return iris
        return []
    
    def _ensure_list(self, data: Any) -> List[Any]:
        """Zajistí že data jsou v podobě listu"""
        if isinstance(data, list):
            return data
        return [data]
    
    def process_file(self, file_path: str, entity_type: str) -> List[Dict[str, Any]]:
        """Zpracuje soubor pomocné entity"""
        logger.info(f"Zpracovávám {file_path} -> {entity_type}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání {file_path}: {e}")
            return []
        
        converted_items = []
        
        # Vyberu správnou konverzi podle typu entity
        converter_map = {
            'PravniAktKomentarFragmentu': self.convert_komentar_fragmentu,
            'PravniAktMetadata': self.convert_metadata,
            'PravniAktVazbaKonsolidacni': self.convert_vazba_konsolidacni,
            'PravniAktOdkaz': self.convert_odkaz,
            'PravniAktSouvisejiciDokument': self.convert_souvisejici_dokument,
            'PravniAktBinarniSoubor': self.convert_binarni_soubor,
            'PravniAktRocnik': self.convert_rocnik_sbirky,
            'PravniAktDigitalniReplika': self.convert_digitalni_replika
        }
        
        converter_func = converter_map.get(entity_type)
        if not converter_func:
            logger.error(f"Neznámý typ entity: {entity_type}")
            return []
        
        # Zpracuj data
        if isinstance(data, list):
            for item in data:
                converted = converter_func(item)
                if isinstance(converted, list):
                    converted_items.extend(converted)
                else:
                    converted_items.append(converted)
        elif isinstance(data, dict):
            if '@graph' in data:
                graph_data = data['@graph']
                if isinstance(graph_data, list):
                    for item in graph_data:
                        converted = converter_func(item)
                        if isinstance(converted, list):
                            converted_items.extend(converted)
                        else:
                            converted_items.append(converted)
                else:
                    converted = converter_func(graph_data)
                    if isinstance(converted, list):
                        converted_items.extend(converted)
                    else:
                        converted_items.append(converted)
            else:
                converted = converter_func(data)
                if isinstance(converted, list):
                    converted_items.extend(converted)
                else:
                    converted_items.append(converted)
        
        return converted_items
    
    def process_all_pomocne_entity(self):
        """Zpracuje všechny pomocné entity"""
        all_converted = []

        for file_name, entity_type in self.file_mapping.items():
            file_path = os.path.join(JSONLD_BASE_PATH, file_name)

            if os.path.isfile(file_path):
                converted_items = self.process_file(file_path, entity_type)
                all_converted.extend(converted_items)
            elif os.path.isdir(file_path):
                # Zpracuj všechny part soubory v adresáři
                for part_file in sorted(Path(file_path).glob("part_*.jsonld")):
                    logger.info(f"Zpracovávám {part_file}")
                    converted_items = self.process_file(str(part_file), entity_type)
                    all_converted.extend(converted_items)
            else:
                logger.warning(f"Soubor nebo adresář {file_path} neexistuje")
        
        # Ulož výsledky
        output_file = self.output_path / "pomocne_entity_converted.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_converted, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Převedeno {len(all_converted)} pomocných entit do {output_file}")
        except Exception as e:
            logger.error(f"Chyba při ukládání: {e}")
        
        # Vytvoř také rozdělené soubory podle typů
        self._save_by_types(all_converted)
    
    def _save_by_types(self, all_converted: List[Dict[str, Any]]):
        """Uloží entity rozdělené podle typů"""
        by_type = {}
        
        for item in all_converted:
            entity_type = item['entity_type']
            if entity_type not in by_type:
                by_type[entity_type] = []
            by_type[entity_type].append(item)
        
        # Ulož každý typ zvlášť
        for entity_type, items in by_type.items():
            output_file = self.output_path / f"{entity_type.lower()}.json"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(items, f, ensure_ascii=False, indent=2, default=str)
                logger.info(f"Uloženo {len(items)} entit typu {entity_type} do {output_file}")
            except Exception as e:
                logger.error(f"Chyba při ukládání {entity_type}: {e}")

def main():
    converter = PomocneEntityConverter()
    converter.process_all_pomocne_entity()

if __name__ == "__main__":
    main()
