import json
import importlib
import re

def pascal_to_snake(name):
    """Převádí název z PascalCase na snake_case."""
    name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()

def get_module_names(config_key):
    """
    Generuje názvy modulů pro konvertor a auditor na základě klíče z konfigurace.
    """
    if config_key.startswith('Vztah_'):
        base_name = pascal_to_snake(config_key.replace('Vztah_', '', 1))
        converter_module = f"sada_vztah_{base_name}"
        auditor_module = f"auditor_vztah_{base_name}"
    else:
        base_name = pascal_to_snake(config_key)
        converter_module = f"sada_{base_name}"
        auditor_module = f"auditor_{base_name}"
    return converter_module, auditor_module

def main():
    """
    Hlavní orchestrační funkce pro spuštění všech konverzí a auditů.
    """
    CONFIG_FILE = 'config.json'

    print("="*60)
    print("ZAHAJUJI PROCES KONVERZE A AUDITU DATOVÝCH SAD")
    print("="*60)

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    except FileNotFoundError:
        print(f"[FAIL] Kritická chyba: Konfigurační soubor '{CONFIG_FILE}' nebyl nalezen.")
        return

    # Získáme všechny klíče kromě speciálního 'neo4j_type_mapping'
    keys_to_process = [k for k in config_data.keys() if k != 'neo4j_type_mapping']

    for key in keys_to_process:
        print(f"\n--- Zpracovávám klíč: '{key}' ---")

        converter_module_name, auditor_module_name = get_module_names(key)

        try:
            # Dynamický import a spuštění konvertoru
            print(f"-> Spouštím konvertor: {converter_module_name}")
            converter_module = importlib.import_module(converter_module_name)
            # Předpokládáme existenci funkce run_conversion v modulu
            converter_module.run_conversion(key)

            # Dynamický import a spuštění auditora
            print(f"-> Spouštím auditor: {auditor_module_name}")
            auditor_module = importlib.import_module(auditor_module_name)
            # Předpokládáme existenci funkce run_audit v modulu
            auditor_module.run_audit(key)

        except ImportError as e:
            print(f"[FAIL] Chyba importu pro klíč '{key}': {e}. Ujistěte se, že soubory '{converter_module_name}.py' a '{auditor_module_name}.py' existují.")
        except Exception as e:
            print(f"[FAIL] Neočekávaná chyba při zpracování klíče '{key}': {e}")

    print("\n="*60)
    print("PROCES KONVERZE A AUDITU DOKONČEN")
    print("="*60)

if __name__ == '__main__':
    main()
