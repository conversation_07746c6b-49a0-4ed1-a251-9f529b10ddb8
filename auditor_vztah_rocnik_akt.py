import json
import csv
from datetime import datetime

def is_valid_type(value, model_type):
    """Robustní funkce pro validaci da<PERSON> typů."""
    if not value:  # <PERSON>r<PERSON><PERSON><PERSON>é hodnoty jsou povoleny
        return True
    
    if model_type == 'Integer':
        try: 
            int(value)
            return True
        except (ValueError, TypeError): 
            return False
    elif model_type == 'String': 
        return isinstance(value, str)
    elif model_type == 'Date':
        try: 
            datetime.strptime(value, '%Y-%m-%d')
            return True
        except (ValueError, TypeError): 
            return False
    elif model_type == 'DateTime':
        for fmt in ('%Y-%m-%dT%H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S'):
            try: 
                datetime.strptime(value, fmt)
                return True
            except (ValueError, TypeError): 
                continue
        return False
    elif model_type == 'Boolean': 
        return value.lower() in ['true', 'false']
    return False

def generate_expected_header(config):
    """Generuje očekávanou hlavičku pro CSV soubor se vztahy a jejich vlastnostmi."""
    type_mapping = config['neo4j_type_mapping']
    start_id_header = f":START_ID({config['start_node']['id_space']})"
    end_id_header = f":END_ID({config['end_node']['id_space']})"
    type_header = ":TYPE"
    prop_headers = []
    for prop, model_type in config.get('properties', {}).items():
        neo4j_type = type_mapping.get(model_type, model_type.lower())
        prop_headers.append(f"{prop}:{neo4j_type}")
    return [start_id_header, end_id_header] + prop_headers + [type_header]

def run_audit(config_key):
    """
    Načte CSV soubor se vztahy a vlastnostmi a zkontroluje ho
    proti pravidlům definovaným v konfiguračním souboru.
    """
    CONFIG_FILE = 'config.json'

    print("="*50)
    print(f"ZAHÁJENÍ AUDITU VZTAHŮ S VLASTNOSTMI: {config_key}")
    print("="*50)

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            config = config_data[config_key]
        print("[INFO] Konfigurační soubor úspěšně načten.")
    except (FileNotFoundError, KeyError) as e:
        print(f"[FAIL] Kritická chyba: Nepodařilo se načíst konfiguraci. {e}")
        return

    csv_file = config['output_file']
    errors_found = 0

    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            try: 
                actual_header = next(reader)
            except StopIteration:
                print(f"[FAIL] Kritická chyba: Soubor '{csv_file}' je prázdný.")
                return

            expected_header = generate_expected_header(config)

            if actual_header != expected_header:
                print("[FAIL] Audit hlavičky selhal!")
                print(f"  Očekávaná hlavička: {expected_header}")
                print(f"  Nalezená hlavička:  {actual_header}")
                return
            else:
                print("[PASS] Audit hlavičky úspěšný.")

            print("[INFO] Zahajuji audit obsahu řádků...")
            for row_num, row in enumerate(reader, 2):
                if len(row) != len(expected_header):
                    print(f"[FAIL] Řádek {row_num}: Nesprávný počet sloupců. Očekáváno {len(expected_header)}, nalezeno {len(row)}.")
                    errors_found += 1
                    continue

                # Validace Start ID, End ID, Type
                if not row[0]: 
                    print(f"[FAIL] Řádek {row_num}: Chybí :START_ID.")
                    errors_found += 1
                if not row[1]: 
                    print(f"[FAIL] Řádek {row_num}: Chybí :END_ID.")
                    errors_found += 1
                if row[-1] != config['relationship_type']:
                    print(f"[FAIL] Řádek {row_num}: Nesprávný :TYPE. Očekáváno '{config['relationship_type']}', nalezeno '{row[-1]}'.")
                    errors_found += 1

                # Validace vlastností a jejich typů
                for i, header_name in enumerate(expected_header[2:-1], 2):
                    cell_value = row[i]
                    if not cell_value: 
                        continue # Nepovinná vlastnost

                    prop_name = header_name.split(':')[0]
                    model_type = config['properties'].get(prop_name)
                    if model_type and not is_valid_type(cell_value, model_type):
                        print(f"[FAIL] Řádek {row_num}, Sloupec '{header_name}': Hodnota '{cell_value}' neodpovídá typu '{model_type}'.")
                        errors_found += 1

    except FileNotFoundError:
        print(f"[FAIL] Kritická chyba: Auditovaný soubor '{csv_file}' nebyl nalezen.")
        return

    print("="*50)
    if errors_found == 0:
        print(f"VÝSLEDEK AUDITU: [PASS]")
        print("Soubor vztahů je v souladu se schváleným modelem.")
    else:
        print(f"VÝSLEDEK AUDITU: [FAIL]")
        print(f"Nalezeno celkem {errors_found} chyb.")
    print("="*50)

if __name__ == '__main__':
    run_audit('Vztah_Rocnik_Akt')
