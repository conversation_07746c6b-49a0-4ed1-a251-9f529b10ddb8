#!/usr/bin/env python3
"""
Hlavní script pro převod JSON-LD souborů E-SBIRKA dat
Založeno na analýze v E-SBIRKA DATA!!! .md

Tento script zpracovává všechny JSON-LD soubory podle jejich typu
a převádí je do strukturovaného formátu pro další zpracování.
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Cesty k JSON-LD souborům
JSONLD_BASE_PATH = r"F:\nocsv\jsonld"
OUTPUT_BASE_PATH = "./converted_data"

# Mapování souborů na typy entit podle analýzy
FILE_ENTITY_MAPPING = {
    # Hlavní entity
    "001PravniAktZneni": "PravniAktZneni",
    "002PravniAkt.jsonld": "PravniAkt", 
    "003PravniAktZneniFragment": "PravniAktZneniFragment",
    "004PravniAktFragment": "PravniAktFragment",
    "005PravniAktKomentarFragmentu.jsonld": "PravniAktKomentarFragmentu",
    "006PravniAktMetadata.jsonld": "PravniAktMetadata",
    "007PravniAktKonsolidacniVazba.jsonld": "PravniAktVazbaKonsolidacni",
    "008PravniAktOdkaz": "PravniAktOdkaz",
    "009PravniAktSouvisejiciDokument.jsonld": "PravniAktSouvisejiciDokument",
    "010PravniAktBinarniSoubor.jsonld": "PravniAktBinarniSoubor",
    "011PravniAktRocnikSbirky.jsonld": "PravniAktRocnik",
    "043PravniAktDigitalniReplika.jsonld": "PravniAktDigitalniReplika",
    
    # Číselníky
    "012CiselnikSbirka.jsonld": "CisSbirka",
    "013CiselnikTypFragmentu.jsonld": "CisTypFragmentu", 
    "014CiselnikTypZneni.jsonld": "CisTypZneni",
    "015CiselnikKategoriePravnichAktu.jsonld": "CisKategoriePravniAkt",
    "016CiselnikTypPravniAkt.jsonld": "CisTypPravniAkt",
    "017CiselnikPodtypuPravnichAktu.jsonld": "CisPodtypPravniAkt",
    "018CiselnikUzemniPlatnost.jsonld": "CisUzemniPlatnost",
    "019CiselnikTypVazba.jsonld": "CisTypVazby",
    "020CiselnikSchvalovatel.jsonld": "CisSchvalovatel",
    "021CiselnikStavVyhlaseniAktu.jsonld": "CisStavVyhlaseniAktu",
    "022CiselnikZobecnenyNazev.jsonld": "CisZobecnenyNazev",
    "023CiselnikSouvisejiciDokument.jsonld": "CisTypSouvisejiciDokument",
    "024CiselnikNovelizacniInstrukce.jsonld": "CisTypNovelizacniInstrukce",
    "025CiselnikTypKomentare.jsonld": "CisTypKomentare",
    "026CiselnikTypDruhuObsahuSouboru.jsonld": "CisTypSouborDruhObsahu",
    "027CiselnikTypExtOdkazu.jsonld": "CisTypExternihoOdkazu",
    "028CiselnikTypVazbaOdkazu.jsonld": "CisTypVazbyOdkazu",
    "029CiselnikTypUsporadaniSbirky.jsonld": "CisTypUsporadaniSbirky",
    "041CiselnikDruhAktu.jsonld": "CisDruhPravniAkt",
    "042CiselnikKompatibilitaEU.jsonld": "CisKompatibilitaEu",
    "044CiselnikDruhPravnihoAktu.jsonld": "CisDruhPravniAkt",
    "045CiselnikSablonaObsahu.jsonld": "CisSablonaObsahu",
    
    # CzechVOC entity
    "030CzechVOCKoncept.jsonld": "CvsKoncept",
    "031CzechVOCDefiniceTerminu.jsonld": "CvsDefiniceTerminu",
    "032CzechVOCTermin.jsonld": "CvsTermin",
    "033CzechVOCPoznamkaKonceptu.jsonld": "CvsPoznamkaKonceptu",
    "034CzechVOCVazbaHierarchie.jsonld": "CvsVazbaTerminHierarchie",
    "035CzechVOCVazbaSouvisejiciTermin.jsonld": "CvsVazbaTerminSouvisejici",
    "036CiselnikCzechVocSchemaKonceptu.jsonld": "CisCvsTypSchemaKonceptu",
    "037CiselnikCzechVocStavKonceptu.jsonld": "CisCvsTypStavKonceptu",
    "038CiselnikCzechVocTypVazbySouvisejiciTermin.jsonld": "CisCvsTypVazbaTerminSouvisejici",
    "039CiselnikCzechVocDefiniceTerminu.jsonld": "CisCvsTypDefiniceTerminu",
    "040CiselnikCzechVocTypPoznamky.jsonld": "CisCvsTypPoznamka"
}

def ensure_output_directory():
    """Vytvoří výstupní adresář pokud neexistuje"""
    Path(OUTPUT_BASE_PATH).mkdir(parents=True, exist_ok=True)

def load_jsonld_file(file_path: str) -> Optional[Dict[str, Any]]:
    """Načte JSON-LD soubor a vrátí jeho obsah"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Chyba při načítání souboru {file_path}: {e}")
        return None

def process_directory_files(directory_path: str, entity_type: str) -> List[Dict[str, Any]]:
    """Zpracuje všechny part_*.jsonld soubory v adresáři"""
    all_data = []
    directory = Path(directory_path)
    
    if not directory.exists():
        logger.warning(f"Adresář {directory_path} neexistuje")
        return all_data
    
    # Najdi všechny part_*.jsonld soubory
    part_files = sorted(directory.glob("part_*.jsonld"))
    
    for part_file in part_files:
        logger.info(f"Zpracovávám {part_file}")
        data = load_jsonld_file(str(part_file))
        if data:
            all_data.append({
                'source_file': str(part_file),
                'entity_type': entity_type,
                'data': data
            })
    
    return all_data

def process_single_file(file_path: str, entity_type: str) -> Optional[Dict[str, Any]]:
    """Zpracuje jednotlivý JSON-LD soubor"""
    logger.info(f"Zpracovávám {file_path}")
    data = load_jsonld_file(file_path)
    if data:
        return {
            'source_file': file_path,
            'entity_type': entity_type,
            'data': data
        }
    return None

def main():
    """Hlavní funkce pro zpracování všech JSON-LD souborů"""
    ensure_output_directory()
    
    all_processed_data = []
    
    for file_key, entity_type in FILE_ENTITY_MAPPING.items():
        file_path = os.path.join(JSONLD_BASE_PATH, file_key)
        
        # Zkontroluj zda je to adresář nebo soubor
        if os.path.isdir(file_path):
            # Zpracuj všechny part soubory v adresáři
            processed_data = process_directory_files(file_path, entity_type)
            all_processed_data.extend(processed_data)
        elif os.path.isfile(file_path):
            # Zpracuj jednotlivý soubor
            processed_data = process_single_file(file_path, entity_type)
            if processed_data:
                all_processed_data.append(processed_data)
        else:
            logger.warning(f"Soubor nebo adresář {file_path} neexistuje")
    
    # Ulož zpracovaná data
    output_file = os.path.join(OUTPUT_BASE_PATH, "all_processed_data.json")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_processed_data, f, ensure_ascii=False, indent=2)
        logger.info(f"Zpracovaná data uložena do {output_file}")
        logger.info(f"Celkem zpracováno {len(all_processed_data)} souborů")
    except Exception as e:
        logger.error(f"Chyba při ukládání výsledků: {e}")

if __name__ == "__main__":
    main()
