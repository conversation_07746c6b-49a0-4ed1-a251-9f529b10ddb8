import json
import csv

def generate_relationship_header(config):
    """Generuje hlavičku pro CSV soubor se vztahy."""
    start_id_header = f":START_ID({config['start_node']['id_space']})"
    end_id_header = f":END_ID({config['end_node']['id_space']})"
    type_header = ":TYPE"
    return [start_id_header, end_id_header, type_header]

def run_conversion(config_key):
    """
    Extrahuje vztahy ze zdrojového JSON souboru a uloží je do CSV
    pro import do Neo4j.
    """
    CONFIG_FILE = 'config.json'

    print(f"Zahajuji extrakci vztahů: {config_key}")

    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            config = config_data[config_key]
        print("Konfigurační soubor ú<PERSON><PERSON> na<PERSON>.")
    except (FileNotFoundError, KeyError) as e:
        print(f"CHYBA: Nepodařilo se načíst konfiguraci. {e}")
        return

    source_file = config['source_file']
    output_file = config['output_file']

    start_key = config['start_node']['source_key']
    end_key = config['end_node']['source_key']
    rel_type = config['relationship_type']

    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        items = data.get('položky', [])
        print(f"Zdrojový soubor '{source_file}' načten, nalezeno {len(items)} položek.")
    except FileNotFoundError:
        print(f"CHYBA: Zdrojový soubor '{source_file}' nebyl nalezen.")
        return
    except json.JSONDecodeError:
        print(f"CHYBA: Zdrojový soubor '{source_file}' není validní JSON.")
        return

    header = generate_relationship_header(config)

    records = []
    for item in items:
        # Filtrujeme pro jistotu, i když by zde měly být jen znění
        if item.get("typ") != "právní-akt-znění":
            continue

        start_id = item.get(start_key)
        end_id = item.get(end_key)

        if start_id and end_id:
            record = {
                header[0]: start_id,
                header[1]: end_id,
                header[2]: rel_type
            }
            records.append(record)

    if not records:
        print("Varování: Nebyly nalezeny žádné vztahy k zápisu.")
        return

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=header)
            writer.writeheader()
            writer.writerows(records)
        print(f"Úspěšně vygenerován soubor vztahů '{output_file}' s {len(records)} záznamy.")
    except IOError as e:
        print(f"CHYBA: Nepodařilo se zapsat do souboru '{output_file}'. {e}")

if __name__ == '__main__':
    run_conversion('Vztah_Zneni_Akt')
