#!/usr/bin/env python3
"""
Script pro převod JSON-LD souborů právních aktů
Zpracovává entity: PravniAkt, PravniAktZneni, PravniAktFragment, PravniAktZneniFragment
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

JSONLD_BASE_PATH = r"F:\nocsv\jsonld"
OUTPUT_BASE_PATH = "./converted_data/pravni_akty"

class PravniAktConverter:
    """Konvertor pro právní akty a jejich znění"""
    
    def __init__(self):
        self.output_path = Path(OUTPUT_BASE_PATH)
        self.output_path.mkdir(parents=True, exist_ok=True)
    
    def convert_pravni_akt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAkt entitu"""
        converted = {
            'entity_type': 'PravniAkt',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        # Mapování vlastností podle analýzy
        property_mapping = {
            'akt-číslo-předpisu': 'aktCisloPredpisu',
            'akt-rok-předpisu': 'aktRokPredpisu', 
            'akt-sbírka-kód': 'aktSbirkaKod',
            'akt-citace': 'aktCitace',
            'akt-název-vyhlášený': 'aktNazevVyhlaseny',
            'akt-kód': 'aktKod',
            'znění-base-id': 'zneniBaseId'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        # Znění aktu
        if 'právní-akt-znění' in data:
            converted['relationships']['MA_ZNENI'] = self._extract_iris(data['právní-akt-znění'])
        
        if 'právní-akt-znění-první' in data:
            converted['relationships']['MA_PRVNI_ZNENI'] = self._extract_iris(data['právní-akt-znění-první'])
            
        if 'právní-akt-znění-poslední' in data:
            converted['relationships']['MA_POSLEDNI_ZNENI'] = self._extract_iris(data['právní-akt-znění-poslední'])
            
        if 'právní-akt-znění-předchozí' in data:
            converted['relationships']['MA_PREDCHOZI_ZNENI'] = self._extract_iris(data['právní-akt-znění-předchozí'])
        
        return converted
    
    def convert_pravni_akt_zneni(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktZneni entitu"""
        converted = {
            'entity_type': 'PravniAktZneni',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        # Mapování vlastností
        property_mapping = {
            'akt-citace': 'aktCitace',
            'akt-název-vyhlášený': 'aktNazevVyhlaseny',
            'znění-id': 'zneniId',
            'znění-kód': 'zneniKod',
            'znění-dokument-id': 'zneniDokumentId',
            'znění-balíček-publikace-id': 'zneniBalicekPublikaceId',
            'znění-datum-čas-poslední-změny': 'zneniDatumCasPosledniZmeny',
            'znění-base-id': 'zneniBaseId',
            'znění-datum-účinnosti-od': 'zneniDatumUcinnostiOd',
            'znění-datum-účinnosti-do': 'zneniDatumUcinnostiDo',
            'znění-eli': 'zneniEli',
            'znění-ročník': 'zneniRocnik',
            'metadata-název-citace': 'metadataNazevCitace',
            'metadata-datum-účinnosti-od': 'metadataDatumUcinnostiOd',
            'metadata-datum-zrušení': 'metadataDatumZruseni'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'datum' in orig_key.lower() and isinstance(value, str):
                    try:
                        # Pokus o parsování data
                        if 'T' in value:
                            converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        else:
                            converted['properties'][new_key] = datetime.fromisoformat(value).date()
                    except:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'akt-iri' in data:
            converted['relationships']['JE_ZNENIM_AKTU'] = [data['akt-iri']]
            
        if 'znění-částka' in data:
            converted['relationships']['VYHLASENO_V_CASTCE'] = [data['znění-částka']]
            
        if 'znění-digitální-replika' in data:
            converted['relationships']['MA_DIGITALNI_REPLIKU'] = self._extract_iris(data['znění-digitální-replika'])
            
        if 'právní-akt-metadata' in data:
            converted['relationships']['MA_METADATA'] = self._extract_iris(data['právní-akt-metadata'])
            
        if 'právní-akt-znění-fragment' in data:
            converted['relationships']['OBSAHUJE_FRAGMENT'] = self._extract_iris(data['právní-akt-znění-fragment'])
            
        if 'právní-akt-související-dokument' in data:
            converted['relationships']['MA_SOUVISEJICI_DOKUMENT'] = self._extract_iris(data['právní-akt-související-dokument'])
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # ZneniCastka
        if 'znění-částka' in data and isinstance(data['znění-částka'], dict):
            converted['nested_entities']['ZneniCastka'] = self._convert_zneni_castka(data['znění-částka'])
            
        # UcinnostTextem
        if 'znění-účinnost-textem' in data:
            converted['nested_entities']['UcinnostTextem'] = self._convert_ucinnost_textem(data['znění-účinnost-textem'])
        
        return converted
    
    def convert_pravni_akt_fragment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktFragment entitu"""
        converted = {
            'entity_type': 'PravniAktFragment',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'fragment-id': 'fragmentId',
            'fragment-base-id': 'fragmentBaseId',
            'fragment-text': 'fragmentText'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'právní-akt-znění-fragment' in data:
            converted['relationships']['MA_ZNENI_FRAGMENT'] = self._extract_iris(data['právní-akt-znění-fragment'])
            
        if 'právní-akt-binární-soubor' in data:
            converted['relationships']['MA_BINARNI_SOUBOR'] = self._extract_iris(data['právní-akt-binární-soubor'])
        
        # Zpracuj vnořené entity
        converted['nested_entities'] = {}
        
        # CvsVyskytPojmu
        if 'cvs-výskyt-pojmu' in data:
            converted['nested_entities']['CvsVyskytPojmu'] = []
            for vyskyt in self._ensure_list(data['cvs-výskyt-pojmu']):
                converted['nested_entities']['CvsVyskytPojmu'].append(self._convert_cvs_vyskyt_pojmu(vyskyt))
        
        return converted
    
    def convert_pravni_akt_zneni_fragment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede PravniAktZneniFragment entitu"""
        converted = {
            'entity_type': 'PravniAktZneniFragment',
            'iri': data.get('iri'),
            'properties': {}
        }
        
        property_mapping = {
            'znění-fragment-id': 'zneniFragmentId',
            'znění-fragment-citace': 'zneniFragmentCitace',
            'znění-fragment-citace-text': 'zneniFragmentCitaceText',
            'znění-fragment-označení-uzlu': 'zneniFragmentOznaceniUzlu',
            'znění-fragment-označení-uzlu-text': 'zneniFragmentOznaceniUzluText',
            'znění-fragment-eli': 'zneniFragmentEli',
            'znění-fragment-url': 'zneniFragmentUrl',
            'znění-fragment-hierarchie': 'zneniFragmentHierarchie',
            'znění-fragment-hierarchie-hex': 'zneniFragmentHierarchieHex',
            'znění-fragment-odlišná-účinnost': 'zneniFragmentOdlisnaUcinnost',
            'znění-fragment-dokument-id': 'zneniFragmentDokumentId'
        }
        
        for orig_key, new_key in property_mapping.items():
            if orig_key in data:
                value = data[orig_key]
                # Převod datumů
                if 'datum' in orig_key.lower() or 'účinnost' in orig_key.lower():
                    if isinstance(value, str):
                        try:
                            converted['properties'][new_key] = datetime.fromisoformat(value.replace('Z', '+00:00')).date()
                        except:
                            converted['properties'][new_key] = value
                    else:
                        converted['properties'][new_key] = value
                else:
                    converted['properties'][new_key] = value
        
        # Zpracuj vztahy
        converted['relationships'] = {}
        
        if 'právní-akt-fragment' in data:
            converted['relationships']['JE_ZNENIM_FRAGMENTU'] = self._extract_iris(data['právní-akt-fragment'])
            
        if 'právní-akt-odkaz' in data:
            converted['relationships']['OBSAHUJE_ODKAZ'] = self._extract_iris(data['právní-akt-odkaz'])
            
        if 'právní-akt-komentář-fragmentu' in data:
            converted['relationships']['MA_KOMENTAR'] = self._extract_iris(data['právní-akt-komentář-fragmentu'])
        
        return converted
    
    def _extract_iris(self, data: Any) -> List[str]:
        """Extrahuje IRI z dat (může být string, dict s iri, nebo list)"""
        if isinstance(data, str):
            return [data]
        elif isinstance(data, dict) and 'iri' in data:
            return [data['iri']]
        elif isinstance(data, list):
            iris = []
            for item in data:
                if isinstance(item, str):
                    iris.append(item)
                elif isinstance(item, dict) and 'iri' in item:
                    iris.append(item['iri'])
            return iris
        return []
    
    def _ensure_list(self, data: Any) -> List[Any]:
        """Zajistí že data jsou v podobě listu"""
        if isinstance(data, list):
            return data
        return [data]
    
    def _convert_zneni_castka(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede ZneniCastka vnořenou entitu"""
        return {
            'entity_type': 'ZneniCastka',
            'properties': {
                'castkaCislo': data.get('částka-číslo'),
                'castkaRok': data.get('částka-rok'),
                'castkaCitace': data.get('částka-citace'),
                'castkaDatumCasVyhlaseni': data.get('částka-datum-čas-vyhlášení'),
                'castkaStranaOd': data.get('částka-strana-od'),
                'castkaStranaDo': data.get('částka-strana-do'),
                'castkaVydavatel': data.get('částka-vydavatel'),
                'castkaIsbn': data.get('částka-isbn'),
                'castkaIssn': data.get('částka-issn'),
                'castkaNazev': data.get('částka-název')
            }
        }
    
    def _convert_ucinnost_textem(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede UcinnostTextem vnořenou entitu"""
        return {
            'entity_type': 'UcinnostTextem',
            'properties': {
                'ucinnostTextem': data.get('účinnost-textem'),
                'ucinnostTextemNazev': data.get('účinnost-textem-název'),
                'ucinnostTextemPracovniDatum': data.get('účinnost-textem-pracovní-datum'),
                'ucinnostTextemSkutecneDatum': data.get('účinnost-textem-skutečné-datum')
            }
        }
    
    def _convert_cvs_vyskyt_pojmu(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede CvsVyskytPojmu vnořenou entitu"""
        return {
            'entity_type': 'CvsVyskytPojmu',
            'properties': {
                'poradiSlova': data.get('pořadí-slova'),
                'pocetSlov': data.get('počet-slov')
            },
            'relationships': {
                'VYSKYT_KONCEPTU': self._extract_iris(data.get('cvs-koncept', []))
            }
        }
    
    def process_files(self):
        """Zpracuje všechny soubory právních aktů"""
        files_to_process = [
            ('002PravniAkt.jsonld', self.convert_pravni_akt),
            ('001PravniAktZneni', self.convert_pravni_akt_zneni),
            ('004PravniAktFragment', self.convert_pravni_akt_fragment),
            ('003PravniAktZneniFragment', self.convert_pravni_akt_zneni_fragment)
        ]
        
        all_converted = []
        
        for file_key, converter_func in files_to_process:
            file_path = os.path.join(JSONLD_BASE_PATH, file_key)
            
            if os.path.isdir(file_path):
                # Zpracuj všechny part soubory
                for part_file in sorted(Path(file_path).glob("part_*.jsonld")):
                    logger.info(f"Zpracovávám {part_file}")
                    try:
                        with open(part_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        if isinstance(data, list):
                            for item in data:
                                converted = converter_func(item)
                                all_converted.append(converted)
                        else:
                            converted = converter_func(data)
                            all_converted.append(converted)
                    except Exception as e:
                        logger.error(f"Chyba při zpracování {part_file}: {e}")
            
            elif os.path.isfile(file_path):
                # Zpracuj jednotlivý soubor
                logger.info(f"Zpracovávám {file_path}")
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if isinstance(data, list):
                        for item in data:
                            converted = converter_func(item)
                            all_converted.append(converted)
                    else:
                        converted = converter_func(data)
                        all_converted.append(converted)
                except Exception as e:
                    logger.error(f"Chyba při zpracování {file_path}: {e}")
        
        # Ulož výsledky
        output_file = self.output_path / "pravni_akty_converted.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_converted, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Převedeno {len(all_converted)} entit do {output_file}")
        except Exception as e:
            logger.error(f"Chyba při ukládání: {e}")

def main():
    converter = PravniAktConverter()
    converter.process_files()

if __name__ == "__main__":
    main()
