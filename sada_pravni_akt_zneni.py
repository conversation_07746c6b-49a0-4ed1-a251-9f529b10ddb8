import json
import csv

def generate_csv_header(config, type_mapping):
    """Generuje hlavičku pro CSV soubor s entitami."""
    
    # ID sloupec
    header = [f"{config['unique_id_source_key']}:ID({config['id_space']})"]
    
    # Vlastnosti s datovými typy
    for prop, model_type in config['properties'].items():
        neo4j_type = type_mapping.get(model_type, model_type.lower())
        header.append(f"{prop}:{neo4j_type}")
    
    # Label sloupec
    header.append(":LABEL")
    
    return header

def run_conversion(config_key):
    """
    Hlavní funkce pro konverzi entity PravniAktZneni ze zdrojového JSON souboru
    do CSV formátu pro import do Neo4j.
    """
    CONFIG_FILE = 'config.json'
    
    print(f"Zahajuji konverzi pro entitu: {config_key}")
    
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            config = config_data[config_key]
            type_mapping = config_data['neo4j_type_mapping']
        print("Konfigurační soubor úspěšně načten.")
    except (FileNotFoundError, KeyError) as e:
        print(f"CHYBA: Nepodařilo se načíst konfiguraci. {e}")
        return
    
    # Podpora pro jeden soubor nebo více souborů
    source_files = config.get('source_files', [config.get('source_file')])
    output_file = config['output_file']

    all_items = []
    for source_file in source_files:
        if not source_file:
            continue
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            items = data.get('položky', [])
            all_items.extend(items)
            print(f"Zdrojový soubor '{source_file}' načten, nalezeno {len(items)} položek.")
        except FileNotFoundError:
            print(f"CHYBA: Zdrojový soubor '{source_file}' nebyl nalezen.")
            continue
        except json.JSONDecodeError:
            print(f"CHYBA: Zdrojový soubor '{source_file}' není validní JSON.")
            continue

    print(f"Celkem načteno {len(all_items)} položek ze všech souborů.")
    
    header = generate_csv_header(config, type_mapping)
    
    records = []
    for item in all_items:
        # Filtrujeme pouze právní akty znění
        if item.get("typ") != "právní-akt-znění":
            continue
        
        # Vytvoříme záznam pro CSV
        record = {}
        
        # ID sloupec
        record[header[0]] = item.get(config['unique_id_source_key'])
        
        # Vlastnosti
        for i, (prop, model_type) in enumerate(config['properties'].items(), 1):
            source_key = config['source_json_key_mapping'][prop]
            value = item.get(source_key, None)
            record[header[i]] = value
        
        # Label sloupec
        record[header[-1]] = config['label']
        
        records.append(record)
    
    if not records:
        print("Varování: Nebyly nalezeny žádné právní akty znění k zápisu.")
        return
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=header)
            writer.writeheader()
            writer.writerows(records)
        print(f"Úspěšně vygenerován soubor '{output_file}' s {len(records)} záznamy.")
    except IOError as e:
        print(f"CHYBA: Nepodařilo se zapsat do souboru '{output_file}'. {e}")

if __name__ == '__main__':
    run_conversion('PravniAktZneni')
