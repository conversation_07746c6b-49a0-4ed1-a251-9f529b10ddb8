#!/usr/bin/env python3
"""
Hlavní orchestrační script pro spuštění všech konverzí JSON-LD souborů
Spouští všechny specializované konvertory v správném pořadí
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from datetime import datetime

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_script(script_name: str) -> bool:
    """Spustí daný script a vrátí True pokud byl úspěšný"""
    logger.info(f"Spouštím {script_name}...")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              encoding='utf-8')
        
        if result.returncode == 0:
            logger.info(f"✓ {script_name} dokončen úspěšně")
            if result.stdout:
                logger.info(f"Výstup: {result.stdout}")
            return True
        else:
            logger.error(f"✗ {script_name} selhal s kódem {result.returncode}")
            if result.stderr:
                logger.error(f"Chyba: {result.stderr}")
            if result.stdout:
                logger.error(f"Výstup: {result.stdout}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Chyba při spouštění {script_name}: {e}")
        return False

def ensure_output_directories():
    """Vytvoří všechny potřebné výstupní adresáře"""
    directories = [
        "./converted_data",
        "./converted_data/pravni_akty",
        "./converted_data/ciselniky", 
        "./converted_data/czechvoc",
        "./converted_data/pomocne_entity"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Vytvořen adresář: {directory}")

def check_prerequisites():
    """Zkontroluje zda existují všechny potřebné scripty a zdrojové soubory"""
    required_scripts = [
        "convert_pravni_akty.py",
        "convert_ciselniky.py", 
        "convert_czechvoc.py",
        "convert_pomocne_entity.py"
    ]
    
    missing_scripts = []
    for script in required_scripts:
        if not os.path.isfile(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        logger.error(f"Chybí následující scripty: {missing_scripts}")
        return False
    
    # Zkontroluj zda existuje zdrojový adresář
    jsonld_path = r"F:\nocsv\jsonld"
    if not os.path.isdir(jsonld_path):
        logger.error(f"Zdrojový adresář {jsonld_path} neexistuje")
        return False
    
    logger.info("✓ Všechny předpoklady jsou splněny")
    return True

def generate_summary_report(results: dict):
    """Vygeneruje souhrnnou zprávu o konverzi"""
    report_file = "./converted_data/conversion_summary.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("SOUHRN KONVERZE JSON-LD SOUBORŮ E-SBIRKA\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Datum a čas konverze: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("VÝSLEDKY KONVERZE:\n")
            f.write("-" * 20 + "\n")
            
            total_success = 0
            total_scripts = len(results)
            
            for script_name, success in results.items():
                status = "✓ ÚSPĚCH" if success else "✗ CHYBA"
                f.write(f"{script_name:<30} {status}\n")
                if success:
                    total_success += 1
            
            f.write(f"\nCelkový výsledek: {total_success}/{total_scripts} scriptů dokončeno úspěšně\n")
            
            if total_success == total_scripts:
                f.write("\n🎉 VŠECHNY KONVERZE DOKONČENY ÚSPĚŠNĚ!\n")
            else:
                f.write(f"\n⚠️  {total_scripts - total_success} scriptů selhalo\n")
            
            f.write("\nSTRUKTURA VÝSTUPNÍCH SOUBORŮ:\n")
            f.write("-" * 30 + "\n")
            f.write("./converted_data/\n")
            f.write("├── pravni_akty/\n")
            f.write("│   └── pravni_akty_converted.json\n")
            f.write("├── ciselniky/\n")
            f.write("│   ├── ciselniky_converted.json\n")
            f.write("│   └── [jednotlivé číselníky].json\n")
            f.write("├── czechvoc/\n")
            f.write("│   ├── czechvoc_converted.json\n")
            f.write("│   └── [jednotlivé entity].json\n")
            f.write("├── pomocne_entity/\n")
            f.write("│   ├── pomocne_entity_converted.json\n")
            f.write("│   └── [jednotlivé entity].json\n")
            f.write("└── conversion_summary.txt\n")
            
        logger.info(f"Souhrnná zpráva uložena do {report_file}")
        
    except Exception as e:
        logger.error(f"Chyba při vytváření souhrnné zprávy: {e}")

def main():
    """Hlavní funkce orchestrace"""
    logger.info("🚀 Spouštím konverzi všech JSON-LD souborů E-SBIRKA")
    logger.info("=" * 60)
    
    # Zkontroluj předpoklady
    if not check_prerequisites():
        logger.error("❌ Konverze přerušena kvůli chybějícím předpokladům")
        sys.exit(1)
    
    # Vytvoř výstupní adresáře
    ensure_output_directories()
    
    # Seznam scriptů k spuštění v pořadí
    scripts_to_run = [
        ("convert_ciselniky.py", "Konverze číselníků"),
        ("convert_czechvoc.py", "Konverze CzechVOC entit"),
        ("convert_pomocne_entity.py", "Konverze pomocných entit"),
        ("convert_pravni_akty.py", "Konverze právních aktů")
    ]
    
    results = {}
    start_time = datetime.now()
    
    # Spusť všechny scripty
    for script_name, description in scripts_to_run:
        logger.info(f"\n📋 {description}")
        logger.info("-" * 40)
        
        success = run_script(script_name)
        results[script_name] = success
        
        if not success:
            logger.warning(f"⚠️  Script {script_name} selhal, ale pokračuji s dalšími...")
    
    # Spočítej celkový čas
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 SOUHRN KONVERZE")
    logger.info("=" * 60)
    
    successful_scripts = sum(1 for success in results.values() if success)
    total_scripts = len(results)
    
    for script_name, success in results.items():
        status = "✓" if success else "✗"
        logger.info(f"{status} {script_name}")
    
    logger.info(f"\nCelkový výsledek: {successful_scripts}/{total_scripts} scriptů úspěšných")
    logger.info(f"Celkový čas: {duration}")
    
    # Vygeneruj souhrnnou zprávu
    generate_summary_report(results)
    
    if successful_scripts == total_scripts:
        logger.info("\n🎉 VŠECHNY KONVERZE DOKONČENY ÚSPĚŠNĚ!")
        logger.info("📁 Výsledky najdete v adresáři ./converted_data/")
    else:
        logger.warning(f"\n⚠️  {total_scripts - successful_scripts} scriptů selhalo")
        logger.info("📋 Podrobnosti najdete v souhrnné zprávě")
    
    logger.info("\n✨ Konverze dokončena!")

if __name__ == "__main__":
    main()
