#!/usr/bin/env python3
"""
Script pro převod JSON-LD souborů číselníků
Zpracovává všechny číselníkové entity podle analýzy v E-SBIRKA DATA!!! .md
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

JSONLD_BASE_PATH = r"F:\nocsv\jsonld"
OUTPUT_BASE_PATH = "./converted_data/ciselniky"

class CiselnikConverter:
    """Konvertor pro číselníky"""
    
    def __init__(self):
        self.output_path = Path(OUTPUT_BASE_PATH)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Mapování souborů na typy číselníků
        self.ciselnik_mapping = {
            "012CiselnikSbirka.jsonld": "CisSbirka",
            "013CiselnikTypFragmentu.jsonld": "CisTypFragmentu", 
            "014CiselnikTypZneni.jsonld": "CisTypZneni",
            "015CiselnikKategoriePravnichAktu.jsonld": "CisKategoriePravniAkt",
            "016CiselnikTypPravniAkt.jsonld": "CisTypPravniAkt",
            "017CiselnikPodtypuPravnichAktu.jsonld": "CisPodtypPravniAkt",
            "018CiselnikUzemniPlatnost.jsonld": "CisUzemniPlatnost",
            "019CiselnikTypVazba.jsonld": "CisTypVazby",
            "020CiselnikSchvalovatel.jsonld": "CisSchvalovatel",
            "021CiselnikStavVyhlaseniAktu.jsonld": "CisStavVyhlaseniAktu",
            "022CiselnikZobecnenyNazev.jsonld": "CisZobecnenyNazev",
            "023CiselnikSouvisejiciDokument.jsonld": "CisTypSouvisejiciDokument",
            "024CiselnikNovelizacniInstrukce.jsonld": "CisTypNovelizacniInstrukce",
            "025CiselnikTypKomentare.jsonld": "CisTypKomentare",
            "026CiselnikTypDruhuObsahuSouboru.jsonld": "CisTypSouborDruhObsahu",
            "027CiselnikTypExtOdkazu.jsonld": "CisTypExternihoOdkazu",
            "028CiselnikTypVazbaOdkazu.jsonld": "CisTypVazbyOdkazu",
            "029CiselnikTypUsporadaniSbirky.jsonld": "CisTypUsporadaniSbirky",
            "041CiselnikDruhAktu.jsonld": "CisDruhPravniAkt",
            "042CiselnikKompatibilitaEU.jsonld": "CisKompatibilitaEu",
            "044CiselnikDruhPravnihoAktu.jsonld": "CisDruhPravniAkt",  # Aktualizovaná verze
            "045CiselnikSablonaObsahu.jsonld": "CisSablonaObsahu",
            # CzechVOC číselníky
            "036CiselnikCzechVocSchemaKonceptu.jsonld": "CisCvsTypSchemaKonceptu",
            "037CiselnikCzechVocStavKonceptu.jsonld": "CisCvsTypStavKonceptu",
            "038CiselnikCzechVocTypVazbySouvisejiciTermin.jsonld": "CisCvsTypVazbaTerminSouvisejici",
            "039CiselnikCzechVocDefiniceTerminu.jsonld": "CisCvsTypDefiniceTerminu",
            "040CiselnikCzechVocTypPoznamky.jsonld": "CisCvsTypPoznamka"
        }
    
    def convert_ciselnik_container(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Převede kontejner číselníku (Ciselnik entitu)"""
        converted = {
            'entity_type': 'Ciselnik',
            'iri': data.get('iri'),
            'properties': {
                'nazevCs': data.get('název.cs')
            }
        }
        return converted
    
    def convert_ciselnik_item(self, data: Dict[str, Any], item_type: str) -> Dict[str, Any]:
        """Převede položku číselníku"""
        converted = {
            'entity_type': item_type,
            'iri': data.get('iri'),
            'properties': {}
        }
        
        # Základní vlastnosti všech položek číselníků
        base_properties = {
            'kód': 'kod',
            'id-esb': 'idEsb',
            'název.cs': 'nazevCs'
        }
        
        for orig_key, new_key in base_properties.items():
            if orig_key in data:
                converted['properties'][new_key] = data[orig_key]
        
        # Specifické vlastnosti podle typu číselníku
        self._add_specific_properties(converted, data, item_type)
        
        return converted
    
    def _add_specific_properties(self, converted: Dict[str, Any], data: Dict[str, Any], item_type: str):
        """Přidá specifické vlastnosti podle typu číselníku"""
        
        # CisPodtypPravniAkt
        if item_type == "CisPodtypPravniAkt":
            if 'pořadí' in data:
                converted['properties']['poradi'] = data['pořadí']
        
        # CisSbirka
        elif item_type == "CisSbirka":
            if 'zkratka.cs' in data:
                converted['properties']['zkratkaCs'] = data['zkratka.cs']
        
        # CisStavVyhlaseniAktu
        elif item_type == "CisStavVyhlaseniAktu":
            if 'je-publikován' in data:
                converted['properties']['jePublikovan'] = data['je-publikován']
        
        # CisSablonaObsahu
        elif item_type == "CisSablonaObsahu":
            if 'verze' in data:
                converted['properties']['verze'] = data['verze']
        
        # CisZobecnenyNazev
        elif item_type == "CisZobecnenyNazev":
            if 'popis.cs' in data:
                converted['properties']['popisCs'] = data['popis.cs']
        
        # CzechVOC číselníky
        elif item_type == "CisCvsTypDefiniceTerminu":
            if 'je-platná' in data:
                converted['properties']['jePlatna'] = data['je-platná']
        
        elif item_type == "CisCvsTypPoznamka":
            if 'je-platná' in data:
                converted['properties']['jePlatna'] = data['je-platná']
        
        elif item_type == "CisCvsTypSchemaKonceptu":
            if 'lze-vytvářet-definice' in data:
                converted['properties']['lzeVytvaretDefinice'] = data['lze-vytvářet-definice']
            if 'info-verze' in data:
                converted['properties']['infoVerze'] = data['info-verze']
        
        elif item_type == "CisCvsTypStavKonceptu":
            if 'je-platná' in data:
                converted['properties']['jePlatna'] = data['je-platná']
            if 'je-publikován' in data:
                converted['properties']['jePublikovan'] = data['je-publikován']
        
        elif item_type == "CisCvsTypVazbaTerminSouvisejici":
            if 'je-platná' in data:
                converted['properties']['jePlatna'] = data['je-platná']
            if 'lze-mezi-schématy' in data:
                converted['properties']['lzeMeziSchematy'] = data['lze-mezi-schématy']
            if 'lze-uvnitř-schéma' in data:
                converted['properties']['lzeUvnitrSchema'] = data['lze-uvnitř-schéma']
    
    def process_ciselnik_file(self, file_path: str, item_type: str) -> List[Dict[str, Any]]:
        """Zpracuje soubor číselníku"""
        logger.info(f"Zpracovávám číselník {file_path} -> {item_type}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání {file_path}: {e}")
            return []
        
        converted_items = []
        
        # Zpracuj data podle struktury
        if isinstance(data, dict):
            # Pokud je to objekt s @graph
            if '@graph' in data:
                graph_data = data['@graph']
                if isinstance(graph_data, list):
                    for item in graph_data:
                        if item.get('@type') == 'číselník':
                            # Kontejner číselníku
                            converted_items.append(self.convert_ciselnik_container(item))
                        else:
                            # Položka číselníku
                            converted_items.append(self.convert_ciselnik_item(item, item_type))
                else:
                    # Jednotlivý objekt
                    if graph_data.get('@type') == 'číselník':
                        converted_items.append(self.convert_ciselnik_container(graph_data))
                    else:
                        converted_items.append(self.convert_ciselnik_item(graph_data, item_type))
            else:
                # Přímý objekt
                if data.get('@type') == 'číselník':
                    converted_items.append(self.convert_ciselnik_container(data))
                else:
                    converted_items.append(self.convert_ciselnik_item(data, item_type))
        
        elif isinstance(data, list):
            # Seznam objektů
            for item in data:
                if item.get('@type') == 'číselník':
                    converted_items.append(self.convert_ciselnik_container(item))
                else:
                    converted_items.append(self.convert_ciselnik_item(item, item_type))
        
        return converted_items
    
    def process_all_ciselniky(self):
        """Zpracuje všechny číselníky"""
        all_converted = []
        
        for file_name, item_type in self.ciselnik_mapping.items():
            file_path = os.path.join(JSONLD_BASE_PATH, file_name)
            
            if os.path.isfile(file_path):
                converted_items = self.process_ciselnik_file(file_path, item_type)
                all_converted.extend(converted_items)
            else:
                logger.warning(f"Soubor {file_path} neexistuje")
        
        # Ulož výsledky
        output_file = self.output_path / "ciselniky_converted.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_converted, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Převedeno {len(all_converted)} položek číselníků do {output_file}")
        except Exception as e:
            logger.error(f"Chyba při ukládání: {e}")
        
        # Vytvoř také rozdělené soubory podle typů
        self._save_by_types(all_converted)
    
    def _save_by_types(self, all_converted: List[Dict[str, Any]]):
        """Uloží číselníky rozdělené podle typů"""
        by_type = {}
        
        for item in all_converted:
            entity_type = item['entity_type']
            if entity_type not in by_type:
                by_type[entity_type] = []
            by_type[entity_type].append(item)
        
        # Ulož každý typ zvlášť
        for entity_type, items in by_type.items():
            output_file = self.output_path / f"{entity_type.lower()}.json"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(items, f, ensure_ascii=False, indent=2, default=str)
                logger.info(f"Uloženo {len(items)} položek typu {entity_type} do {output_file}")
            except Exception as e:
                logger.error(f"Chyba při ukládání {entity_type}: {e}")

def main():
    converter = CiselnikConverter()
    converter.process_all_ciselniky()

if __name__ == "__main__":
    main()
